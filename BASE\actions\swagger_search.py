import json
from typing import Any
from BASE.embeddings.embeddings import generate_embeddings_cloud
from BASE.vdb.qdrant import get_qdrant_client
import requests
import constants
from BASE.services.swagger.llm_service import swagger_query_api

async def _perform_swagger_search(
    query: str,
    index_name: str,
    llm_config: dict,
    limit: int = 20,
) -> list[dict[str, Any]]:
    qc = get_qdrant_client()

    try:
        query_embeddings = await generate_embeddings_cloud(False, query)
        results = await qc.search(
            collection_name=index_name,
            query_vector=("vectors", query_embeddings),
            limit=limit,
            with_payload=True,
        )

        # name -> swagger endpoint spec
        results_map: dict[str, dict[str, Any]] = {}
        for result in results:
            metadata = result.payload
            results_map[metadata.get("name", "")] = metadata.get("additional_metadata", {})

        api_results = await swagger_query_api(query, results, llm_config)
        filtered_results = list(map(lambda x: results_map[x], api_results))
        return filtered_results
    except Exception as e:
        raise


async def process_swagger_search(
    query: str, tool_id: str, kbid: str, search_references, llm_config: dict
):
    """Process swagger search actions."""
    try:
        search_results = await _perform_swagger_search(query=query, index_name=kbid, llm_config=llm_config
        )

        for result in search_results:
            search_references.add_search_result(
                type="file",
                name=result.get("name", ""),
                path=result.get("name", ""),
                content=json.dumps(result.get("additional_metadata", {})),
            )

        final_content = json.dumps(search_results)

        status = "error"

        if final_content:
            status = "success"

        return {"status": status, "content": final_content}, search_references

    except Exception as e:
        print(f"Error in process_swagger_search: {e}")
        raise
