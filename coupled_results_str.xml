<endpoint>
  <path>/nge/prod/nge-api/api/persons/{personId}/chart/allergies</path>
  <summary>Retrieves a paginated list of allergy summaries for a specified patient identified by personId, supporting OData query parameters for filtering ($filter), sorting ($orderby), pagination ($top, $skip), and count options ($inlinecount, $count). Requires Accept header and returns detailed allergy records including descriptions, onset/resolution dates, reactions, and related metadata. Successful responses provide allergy items with navigation links and total counts; errors return standard HTTP status codes with messages. Ideal for clinical applications needing filtered allergy data per patient with flexible query control.</summary>
</endpoint>