from litellm import acompletion
import json
import re
from typing import List, Dict, Any
from logger.log import logger
from prompts import PROMPTS

async def generate_queries(query: str, llm_config: dict, num_prompts: int = 5) -> List[str]:
    """
    Generate search queries for Swagger API documentation using LLM.
    
    Args:
        query: User's original query
        llm_config: LLM configuration dict with base_url, api_key, model
        num_prompts: Number of search prompts to generate
        
    Returns:
        List of generated search prompts
    """
    try:
        logger.info(f"Generating {num_prompts} search queries")
        
        system_prompt = await PROMPTS.get("swagger_queries")
        
        user_prompt = f"""Break down the following query into {num_prompts} concise search prompts:

{query}

Generate specific search terms that would help find relevant API endpoints, parameters, and responses."""


        model_name = llm_config.get("model","")
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://backend.v3.codemateai.dev/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.3,
            stream=False
        )

        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            # Parse the numbered list response
            sub_prompts = []
            lines = content.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if line:
                    # Remove numbering (1., 2., etc.) and clean up
                    cleaned_line = re.sub(r'^\d+\.\s*', '', line)
                    cleaned_line = cleaned_line.strip('- ')
                    if cleaned_line:
                        sub_prompts.append(cleaned_line)
            
            logger.info(f"Generated {len(sub_prompts)} search queries")
            return sub_prompts[:num_prompts]  # Ensure we don't exceed requested number
        
        logger.warning("No valid response from LLM for query generation")
        return [query]  # Fallback to original query
        
    except Exception as e:
        logger.error(f"Error generating search queries: {e}")
        return [query]  # Fallback to original query


async def generate_calls(query: str, search_results: List[Dict], spec_endpoints: List[Dict], llm_config: dict) -> List[Dict]:
    """
    Generate API calls based on user query and search results using LLM.
    
    Args:
        query: User's original query
        search_results: Results from swagger search
        spec_endpoints: Available endpoints from swagger spec
        llm_config: LLM configuration dict
        
    Returns:
        List of generated API call sequences
    """
    try:
        logger.info(f"Generating API calls for query: {query[:100]}...")
        
        system_prompt = """You are an API integration expert. Given a user query and available API endpoints, 
generate a sequence of API calls that would fulfill the user's request.

For each API call, provide:
1. The endpoint path
2. The HTTP method
3. Required parameters
4. The sequence order
5. Brief description of what this call does

Return your response as a JSON array of objects with this structure:
{
  "path": "/api/endpoint",
  "method": "GET",
  "sequence": 1,
  "description": "What this call does",
  "parameters": {...}
}"""

        # Prepare context about available endpoints
        endpoints_context = ""
        if spec_endpoints:
            endpoints_context = "Available API endpoints:\n"
            for i, endpoint in enumerate(spec_endpoints[:10]):  # Limit to first 10 to avoid token limits
                endpoints_context += f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}\n"
        
        # Prepare search results context
        search_context = ""
        if search_results:
            search_context = "Relevant search results:\n"
            for i, result in enumerate(search_results[:5]):  # Limit to first 5
                if 'metadata' in result and 'endpoint' in result['metadata']:
                    endpoint = result['metadata']['endpoint']
                    search_context += f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}: {endpoint.get('summary', 'No description')}\n"

        user_prompt = f"""User Query: {query}

{endpoints_context}

{search_context}

Based on the user query and available endpoints, generate a sequence of API calls that would fulfill the request. 
Return only the JSON array, no additional text."""
        model_name = llm_config.get("model","")

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://backend.v3.codemateai.dev/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.4,
            stream=False
        )

        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            try:
                # Try to parse JSON response
                if content.strip().startswith('['):
                    calls = json.loads(content)
                    logger.info(f"Generated {len(calls)} API calls")
                    return calls
                else:
                    # Try to extract JSON from response
                    json_match = re.search(r'\[.*\]', content, re.DOTALL)
                    if json_match:
                        calls = json.loads(json_match.group())
                        logger.info(f"Generated {len(calls)} API calls")
                        return calls
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
        
        logger.warning("No valid API calls generated")
        return []
        
    except Exception as e:
        logger.error(f"Error generating API calls: {e}")
        return []


async def structure_output(structure_prompt: str, swagger_output: Dict, llm_config: dict) -> Dict:
    """
    Apply structure formatting to swagger output using LLM.
    
    Args:
        structure_prompt: Instructions for how to structure the output
        swagger_output: The swagger response to be structured
        llm_config: LLM configuration dict
        
    Returns:
        Structured output dictionary
    """
    try:
        logger.info("Applying structure to swagger output")
        
        system_prompt = """You are a data formatting expert. Given a structure prompt and swagger API output, 
reformat the output according to the specified structure while preserving all important information.

Return the result as valid JSON that follows the requested structure."""

        user_prompt = f"""Structure Instructions: {structure_prompt}

Original Swagger Output:
{json.dumps(swagger_output, indent=2)}

Please reformat this output according to the structure instructions. Return only the JSON result."""
        model_name = llm_config.get("model","")

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://backend.v3.codemateai.dev/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.2,
            stream=False
        )

        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            
            try:
                # Try to parse JSON response
                if content.strip().startswith('{'):
                    structured_output = json.loads(content)
                    logger.info("Successfully applied structure to output")
                    return structured_output
                else:
                    # Try to extract JSON from response
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        structured_output = json.loads(json_match.group())
                        logger.info("Successfully applied structure to output")
                        return structured_output
                    
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse structured JSON response: {e}")
        
        logger.warning("Failed to apply structure, returning original output")
        return swagger_output
        
    except Exception as e:
        logger.error(f"Error applying structure to output: {e}")
        return swagger_output




async def extract_summary(content: str) -> str:
    """Extract summary from content wrapped in <summary> tags"""
    if "<summary>" in content and "</summary>" in content:
        try:
            summary = content.split("<summary>")[-1].split("</summary>")[0].strip()
            return summary
        except (ValueError, IndexError):
            logger.warning("Failed to extract summary")
    return content


async def get_endpoint_summary_simple(endpoint_dict: dict, llm_config: dict) -> str:
    """
    Generate a concise summary of an API endpoint using LLM.
    
    Args:
        endpoint_dict: Dictionary containing endpoint details
        llm_config: LLM configuration dictionary
        
    Returns:
        Formatted summary string wrapped in <summary> tags
    """
    try:
        logger.info(f"Generating summary for endpoint: {json.dumps(endpoint_dict)[:100]}...")
        
        system_prompt = await PROMPTS.get("swagger_summary")
        
        user_prompt = f"""Generate a clear, concise summary and practical use cases for this API endpoint.
Focus on key functionality, parameters, and expected outcomes.

STRICT REQUIREMENTS:
- Summary must be 5 lines or less
- Include main purpose, key parameters, and expected response
- Be specific and practical
- Use clear, technical language

Endpoint specification:
{json.dumps(endpoint_dict, indent=2)}

Format: Wrap the summary in <summary> tags."""

        model_name = llm_config.get("model","")
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        logger.info(f"llm config: {json.dumps(llm_config, indent=2)}")

        response = await acompletion(
            base_url=llm_config.get("base_url", "https://backend.v3.codemateai.dev/v1"),
            api_key=llm_config.get("api_key", ""),
            model=model_name,
            messages=messages,
            temperature=0.3,
            stream=False
        )


        content = response.choices[0].message.content.strip()
        
        # Ensure proper summary tag wrapping
        summary = await extract_summary(content)
        return summary
        
    except Exception as e:
        logger.error(f"Error generating endpoint summary: {e}")
        return "Error generating summary"
    

async def swagger_query_api(query, results, llm_config: dict):

    results_xml: list[str] = []
    for result in results:
        metadata = result.payload
        endpoint_str = "<endpoint>\n"
        endpoint_str += f"  <path>{metadata.get('name', '')}</path>\n"
        endpoint_str += f"  <summary>{metadata.get('content', '')}</summary>\n"
        endpoint_str += "</endpoint>"
        results_xml.append(endpoint_str)
    with open("coupled_results_str.xml", "w") as f:
        f.write("\n".join(results_xml))

    system_prompt = await PROMPTS.get("swagger_query_api")

    user_prompt = f"""
Following are the search results from a swagger API spec. Your task is to select the most relevant endpoints paths based on the provided query.

<query>{query}</query>
<data>{results_xml}</data>

Expected output format:
<paths>
path1
path2
path3
</paths>

If the search results are not relevant to the query, return the paths as:
<paths>
</paths>

STRICT INSTRUCTION:
When returning the paths, make sure to return the exact paths as provided. Do not add any other paths.
YOU MUST PROVIDE AT LEAST 2 PATHS IRRESPECTIVE OF THE QUERY.
"""

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]


    response = await acompletion(
        base_url=llm_config.get("base_url", "https://backend.v3.codemateai.dev/v1"),
        api_key=llm_config.get("api_key", ""),
        model=llm_config.get("model", ""),
        messages=messages,
        temperature=0.3,
        stream=False
    )

    response = response.choices[0].message.content.strip()

    if "<paths>" in response and "</paths>" in response:
        paths_section = response.split("<paths>")[-1].split("</paths>")[0].strip()
        paths = paths_section.split("\n")
        paths = [path.strip() for path in paths if path.strip()]
        return paths
    else:
        return []
